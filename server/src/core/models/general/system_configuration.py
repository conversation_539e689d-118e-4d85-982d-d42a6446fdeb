"""System Configuration Database Models.

SQLAlchemy models for electrical system configuration management with
IEEE/IEC standards compliance and user preference management.

Key models:
- SystemConfiguration: Global and project-specific electrical system settings
"""

import datetime
from typing import TYPE_CHECKING, List, Optional
import uuid

from sqlalchemy import <PERSON><PERSON>an, DateTime, ForeignKey, String, Text, UniqueConstraint, JSON, Float
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import UUID

from src.core.models.base import Base, CommonColumns, SoftDeleteColumns, EnumType
from src.core.enums.common_enums import TemperatureUnit, VoltageClass, FrequencyType
from src.core.utils.datetime_utils import utcnow_naive

if TYPE_CHECKING:
    from .project import Project


class SystemConfiguration(CommonColumns, SoftDeleteColumns, Base):
    """System configuration model for electrical system settings.

    Manages both global system configuration and project-specific overrides
    for electrical design parameters, calculation methods, and compliance settings.

    Attributes:
        config_id: Unique UUID identifier for the configuration
        project_id: Optional foreign key for project-specific configurations (null for global)
        scope: Configuration scope (global, project, user)
        category: Configuration category (calculation, display, standards, etc.)
        voltage_system: Default voltage system configuration
        frequency_system: Default frequency configuration (50Hz/60Hz)
        temperature_unit: Default temperature unit (Celsius/Fahrenheit)
        calculation_precision: Number of decimal places for calculations
        rounding_method: Rounding method for calculations
        safety_factors: JSON configuration of safety factors by equipment type
        environmental_conditions: JSON of default environmental parameters
        standards_compliance: JSON array of applicable electrical standards
        validation_rules: JSON configuration of validation rules
        is_active: Whether this configuration is currently active
        project: Related project entity (if project-specific)
    """

    __tablename__ = "system_configurations"

    # Unique configuration identifier (UUID)
    config_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        default=lambda: str(uuid.uuid4()),
        unique=True,
        nullable=False,
        index=True,
        comment="Unique UUID identifier for the configuration",
    )

    # Project relationship (optional - null for global configurations)
    project_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("projects.id"),
        nullable=True,
        index=True,
        comment="Optional foreign key for project-specific configurations",
    )

    # Configuration scope and category
    scope: Mapped[str] = mapped_column(
        String(50), nullable=False, index=True, default="global", comment="Configuration scope (global, project, user)"
    )

    category: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        index=True,
        comment="Configuration category (calculation, display, standards, etc.)",
    )

    # Electrical system defaults
    voltage_system: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True, comment="Default voltage system configuration"
    )

    frequency_system: Mapped[Optional[FrequencyType]] = mapped_column(
        EnumType(FrequencyType), nullable=True, comment="Default frequency configuration (50Hz/60Hz)"
    )

    temperature_unit: Mapped[Optional[TemperatureUnit]] = mapped_column(
        EnumType(TemperatureUnit), nullable=True, default=TemperatureUnit.CELSIUS, comment="Default temperature unit"
    )

    # Calculation settings
    calculation_precision: Mapped[int] = mapped_column(
        nullable=False, default=2, comment="Number of decimal places for calculations"
    )

    rounding_method: Mapped[str] = mapped_column(
        String(20), nullable=False, default="round_half_up", comment="Rounding method for calculations"
    )

    # Configuration data (JSON)
    safety_factors: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON configuration of safety factors by equipment type"
    )

    environmental_conditions: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON of default environmental parameters"
    )

    standards_compliance: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON array of applicable electrical standards"
    )

    validation_rules: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON configuration of validation rules"
    )

    # Status
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        comment="Whether this configuration is currently active",
    )

    # Relationships
    project: Mapped[Optional["Project"]] = relationship("Project", foreign_keys=[project_id])

    __table_args__ = (
        UniqueConstraint("config_id", name="uq_system_config_id"),
        UniqueConstraint("project_id", "category", "scope", name="uq_project_config_category"),
    )

    def __repr__(self) -> str:
        scope_info = f"project={self.project_id}" if self.project_id else "global"
        return f"<SystemConfiguration(config_id={self.config_id}, category='{self.category}', scope={scope_info})>"


# The following classes will be added in future migrations:
# - ElectricalStandard
# - UserPreferences  
# - ConfigurationTemplate
