"""Add SystemConfiguration table

Revision ID: 6ae40dcdef5b
Revises: a27501974d9d
Create Date: 2025-08-11 19:29:12.459664

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "6ae40dcdef5b"
down_revision = "a27501974d9d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "system_configurations",
        sa.Column(
            "config_id", sa.UUID(as_uuid=False), nullable=False, comment="Unique UUID identifier for the configuration"
        ),
        sa.Column(
            "project_id",
            sa.Integer(),
            nullable=True,
            comment="Optional foreign key for project-specific configurations",
        ),
        sa.Column("scope", sa.String(length=50), nullable=False, comment="Configuration scope (global, project, user)"),
        sa.Column(
            "category",
            sa.String(length=100),
            nullable=False,
            comment="Configuration category (calculation, display, standards, etc.)",
        ),
        sa.Column(
            "voltage_system", sa.String(length=50), nullable=True, comment="Default voltage system configuration"
        ),
        sa.Column(
            "frequency_system", sa.String(), nullable=True, comment="Default frequency configuration (50Hz/60Hz)"
        ),
        sa.Column("temperature_unit", sa.String(), nullable=True, comment="Default temperature unit"),
        sa.Column(
            "calculation_precision", sa.Integer(), nullable=False, comment="Number of decimal places for calculations"
        ),
        sa.Column("rounding_method", sa.String(length=20), nullable=False, comment="Rounding method for calculations"),
        sa.Column(
            "safety_factors", sa.JSON(), nullable=True, comment="JSON configuration of safety factors by equipment type"
        ),
        sa.Column(
            "environmental_conditions", sa.JSON(), nullable=True, comment="JSON of default environmental parameters"
        ),
        sa.Column(
            "standards_compliance", sa.JSON(), nullable=True, comment="JSON array of applicable electrical standards"
        ),
        sa.Column("validation_rules", sa.JSON(), nullable=True, comment="JSON configuration of validation rules"),
        sa.Column("is_active", sa.Boolean(), nullable=False, comment="Whether this configuration is currently active"),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("config_id", name="uq_system_config_id"),
        sa.UniqueConstraint("project_id", "category", "scope", name="uq_project_config_category"),
    )
    op.create_index(op.f("ix_system_configurations_category"), "system_configurations", ["category"], unique=False)
    op.create_index(op.f("ix_system_configurations_config_id"), "system_configurations", ["config_id"], unique=True)
    op.create_index(op.f("ix_system_configurations_is_active"), "system_configurations", ["is_active"], unique=False)
    op.create_index(op.f("ix_system_configurations_project_id"), "system_configurations", ["project_id"], unique=False)
    op.create_index(op.f("ix_system_configurations_scope"), "system_configurations", ["scope"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_system_configurations_scope"), table_name="system_configurations")
    op.drop_index(op.f("ix_system_configurations_project_id"), table_name="system_configurations")
    op.drop_index(op.f("ix_system_configurations_is_active"), table_name="system_configurations")
    op.drop_index(op.f("ix_system_configurations_config_id"), table_name="system_configurations")
    op.drop_index(op.f("ix_system_configurations_category"), table_name="system_configurations")
    op.drop_table("system_configurations")
    # ### end Alembic commands ###
