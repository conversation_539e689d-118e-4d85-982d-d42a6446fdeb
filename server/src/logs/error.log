2025-08-11 19:20:41.390 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:367 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-08-11 19:20:41.390 | ERROR    | MainProcess:MainThread | src.core.errors.unified_error_handler:async_wrapper:653 - An unexpected error occurred in async_wrapper
Traceback (most recent call last):

  File "/home/<USER>/dev/ued/server/src/core/errors/unified_error_handler.py", line 684, in sync_wrapper
    return func(*args, **kwargs)
           │     │       └ {}
           │     └ ()
           └ <function get_async_engine at 0x7f2d7e242ca0>

  File "/home/<USER>/dev/ued/server/src/core/database/engine.py", line 154, in get_async_engine
    raise RuntimeError("Async database engine not initialized. Call initialize_database_engine() first.")

RuntimeError: Async database engine not initialized. Call initialize_database_engine() first.


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/home/<USER>/dev/ued/server/src/main.py", line 153, in <module>
    cli_app()
    └ <typer.main.Typer object at 0x7f2d7f9de270>

  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/typer/main.py", line 324, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x7f2d7f9de270>
           └ <function get_command at 0x7f2d80cf28e0>
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x7f2d80cf0d60>
           └ <TyperGroup ultimate-electrical-designer>
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/typer/core.py", line 757, in main
    return _main(
           └ <function _main at 0x7f2d80ce7e20>
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/typer/core.py", line 195, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x7f2d7f9decf0>
         │    └ <function Group.invoke at 0x7f2d81402de0>
         └ <TyperGroup ultimate-electrical-designer>
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x7f2d51103c50>
           │               │       │       └ <function Command.invoke at 0x7f2d81401bc0>
           │               │       └ <TyperCommand seed-general-data>
           │               └ <click.core.Context object at 0x7f2d51103c50>
           └ <function Group.invoke.<locals>._process_result at 0x7f2d5111ede0>
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {}
           │   │      │    │           └ <click.core.Context object at 0x7f2d51103c50>
           │   │      │    └ <function seed_general_data_command at 0x7f2d5111eb60>
           │   │      └ <TyperCommand seed-general-data>
           │   └ <function Context.invoke at 0x7f2d81400e00>
           └ <click.core.Context object at 0x7f2d51103c50>
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {}
           │         └ ()
           └ <function seed_general_data_command at 0x7f2d5111eb60>
  File "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/typer/main.py", line 699, in wrapper
    return callback(**use_params)
           │          └ {}
           └ <function seed_general_data_command at 0x7f2d5111e840>

  File "/home/<USER>/dev/ued/server/src/main.py", line 144, in seed_general_data_command
    asyncio.run(seed_general_data())
    │       │   └ <function seed_general_data at 0x7f2d4b7940e0>
    │       └ <function run at 0x7f2d816e34c0>
    └ <module 'asyncio' from '/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/__init__.py'>

  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object seed_general_data at 0x7f2d5112da80>
           │      └ <function Runner.run at 0x7f2d814e5e40>
           └ <asyncio.runners.Runner object at 0x7f2d7f9def90>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<seed_general_data() running at /home/<USER>/dev/ued/server/data/seed_general.py:1039> cb=[_r...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x7f2d814e39c0>
           │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7f2d7f9def90>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x7f2d814e3920>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x7f2d814e5760>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x7f2d81694680>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x7f2d5197d1e0>()>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x7f2d5197d1e0>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x7f2d5197d1e0>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x7f2d5197d1e0>()>

  File "/home/<USER>/dev/ued/server/data/seed_general.py", line 1039, in seed_general_data
    async with get_async_db_session() as db:
               └ <function get_async_db_session at 0x7f2d7dbbd300>

  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 214, in __aenter__
    return await anext(self.gen)
                       │    └ <async_generator object get_async_db_session at 0x7f2d5112e340>
                       └ <contextlib._AsyncGeneratorContextManager object at 0x7f2d7f9dfb60>

  File "/home/<USER>/dev/ued/server/src/core/database/session.py", line 424, in get_async_db_session
    session_factory = await get_async_session_factory()
                            └ <function get_async_session_factory at 0x7f2d7dbbc540>

> File "/home/<USER>/dev/ued/server/src/core/errors/unified_error_handler.py", line 647, in async_wrapper
    return await func(*args, **kwargs)
                 │     │       └ {}
                 │     └ ()
                 └ <function get_async_session_factory at 0x7f2d7dbbc4a0>

  File "/home/<USER>/dev/ued/server/src/core/database/session.py", line 41, in get_async_session_factory
    engine = get_async_engine()
             └ <function get_async_engine at 0x7f2d7e242d40>

  File "/home/<USER>/dev/ued/server/src/core/errors/unified_error_handler.py", line 709, in sync_wrapper
    raise DatabaseError(
          └ <class 'src.core.errors.exceptions.DatabaseError'>

src.core.errors.exceptions.DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
2025-08-11 19:20:41.400 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:367 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-08-11 19:20:41.402 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:147 - Error seeding general data: Database error: Database operation failed: An unexpected internal error occurred.
2025-08-11 19:21:13.001 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:154 - Error seeding general data: object Engine can't be used in 'await' expression
2025-08-11 19:22:17.363 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:158 - Error seeding general data: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.NotNullViolationError'>: null value in column "start_date" of relation "project_phases" violates not-null constraint
DETAIL:  Failing row contains (685d86ae-e5af-4a06-ba55-4ef8e0e4f34c, 1, Planning, null, null, 100, t, null, null, null, 1, Project Planning, 2025-08-11 16:22:17.355739, 2025-08-11 16:22:17.355741, f, null, null).
[SQL: INSERT INTO project_phases (phase_id, project_id, phase_type, start_date, end_date, progress_percentage, is_active, notes, name, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) SELECT p0::UUID, p1::INTEGER, p2::VARCHAR, p3::TIMEST ... 2697 characters truncated ...  p12, p13, sen_counter) ORDER BY sen_counter RETURNING project_phases.id, project_phases.id AS id__1]
[parameters: ('685d86ae-e5af-4a06-ba55-4ef8e0e4f34c', 1, 'Planning', None, None, 100.0, True, None, 'Project Planning', datetime.datetime(2025, 8, 11, 16, 22, 17, 355739), datetime.datetime(2025, 8, 11, 16, 22, 17, 355741), False, None, None, 'fc25f087-dd1f-44f4-83d3-6b8055b40772', 1, 'Conceptual Design', None, None, 75.0, True, None, 'Conceptual Design', datetime.datetime(2025, 8, 11, 16, 22, 17, 355743), datetime.datetime(2025, 8, 11, 16, 22, 17, 355743), False, None, None, '0ba56a77-6dd1-4628-a20a-48017b0c6ae8', 1, 'Schematic Design', None, None, 25.0, True, None, 'Schematic Design', datetime.datetime(2025, 8, 11, 16, 22, 17, 355744), datetime.datetime(2025, 8, 11, 16, 22, 17, 355745), False, None, None, '26b2e126-000b-43bd-8aa8-413ede02ef14', 1, 'Design Development', None, None, 0.0, True, None ... 12 parameters truncated ... True, None, 'Project Planning', datetime.datetime(2025, 8, 11, 16, 22, 17, 355746), datetime.datetime(2025, 8, 11, 16, 22, 17, 355747), False, None, None, '5dc66e2a-eaf3-4e27-baf4-52a244df3bbc', 2, 'Conceptual Design', None, None, 0.0, True, None, 'Conceptual Design', datetime.datetime(2025, 8, 11, 16, 22, 17, 355747), datetime.datetime(2025, 8, 11, 16, 22, 17, 355747), False, None, None, '8693bb58-f931-40fe-bbe3-738dc929606d', 2, 'Schematic Design', None, None, 0.0, True, None, 'Schematic Design', datetime.datetime(2025, 8, 11, 16, 22, 17, 355748), datetime.datetime(2025, 8, 11, 16, 22, 17, 355748), False, None, None, '5be2f177-3c67-4acf-b770-6788d22e7fa8', 2, 'Design Development', None, None, 0.0, True, None, 'Design Development', datetime.datetime(2025, 8, 11, 16, 22, 17, 355749), datetime.datetime(2025, 8, 11, 16, 22, 17, 355749), False, None, None)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-11 19:23:14.344 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:158 - Error seeding general data: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.NotNullViolationError'>: null value in column "title" of relation "project_milestones" violates not-null constraint
DETAIL:  Failing row contains (0264db1a-2e31-4026-bbaa-acb166edf152, 2, null, All project requirements documented, null, null, Completed, null, null, null, 1, Requirements Gathering Complete, null, 2025-08-11 16:23:14.339107, 2025-08-11 16:23:14.33911, f, null, null).
[SQL: INSERT INTO project_milestones (milestone_id, phase_id, title, description, due_date, completion_date, status, assigned_user_id, name, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) SELECT p0::UUID, p1::INTEGER, p2::VARCHA ... 5479 characters truncated ... 4, sen_counter) ORDER BY sen_counter RETURNING project_milestones.id, project_milestones.id AS id__1]
[parameters: ('0264db1a-2e31-4026-bbaa-acb166edf152', 2, None, 'All project requirements documented', None, None, 'Completed', None, 'Requirements Gathering Complete', None, datetime.datetime(2025, 8, 11, 16, 23, 14, 339107), datetime.datetime(2025, 8, 11, 16, 23, 14, 339110), False, None, None, '0f3bcfc0-9d09-4605-afa7-0beb74518eb5', 2, None, 'Project charter signed off by stakeholders', None, None, 'Completed', None, 'Project Charter Approved', None, datetime.datetime(2025, 8, 11, 16, 23, 14, 339111), datetime.datetime(2025, 8, 11, 16, 23, 14, 339111), False, None, None, '6075d4e7-3bcf-4c3b-b0cc-2b91e462f983', 3, None, 'Initial design concept reviewed and approved', None, None, 'Completed', None, 'Concept Design Review', None, datetime.datetime(2025, 8, 11, 16, 23, 14, 339112), datetime.datetime(2025, 8, 11, 16, 23, 14, 339112), False, None, None, '5b67a898-e65d-42d8-b5bd-55e83dece766', 3, None, 'Electrical load analysis completed', None ... 140 parameters truncated ... datetime.datetime(2025, 8, 11, 16, 23, 14, 339120), datetime.datetime(2025, 8, 11, 16, 23, 14, 339121), False, None, None, '853c0f35-8e64-4056-984d-0d35f897dce8', 8, None, 'Major equipment specifications defined', None, None, 'Planned', None, 'Equipment Specifications', None, datetime.datetime(2025, 8, 11, 16, 23, 14, 339121), datetime.datetime(2025, 8, 11, 16, 23, 14, 339122), False, None, None, '4d2f5a24-dc28-41d4-a0c6-0ce6daf7e3d4', 9, None, 'All detailed electrical drawings completed', None, None, 'Planned', None, 'Detailed Drawings Complete', None, datetime.datetime(2025, 8, 11, 16, 23, 14, 339122), datetime.datetime(2025, 8, 11, 16, 23, 14, 339122), False, None, None, 'ea8cee9b-315f-4c75-a8ea-c830c8f82dd8', 9, None, 'All panel schedules and layouts finalized', None, None, 'Planned', None, 'Panel Schedules Finalized', None, datetime.datetime(2025, 8, 11, 16, 23, 14, 339123), datetime.datetime(2025, 8, 11, 16, 23, 14, 339123), False, None, None)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-11 19:24:07.104 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:158 - Error seeding general data: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedTableError'>: relation "system_configurations" does not exist
[SQL: SELECT system_configurations.config_id 
FROM system_configurations]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-11 19:30:36.837 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:367 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-08-11 19:31:26.754 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:367 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-08-11 19:32:06.214 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:158 - Error seeding general data: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.DataError'>: invalid input for query argument $31: datetime.datetime(2025, 8, 11, 16, 32, 6... (can't subtract offset-naive and offset-aware datetimes)
[SQL: INSERT INTO synchronization_logs (project_id, session_id, user_id, operation_type, sync_direction, status, started_at, completed_at, duration_ms, source_database_url, target_database_url, records_processed, records_created, records_updated, records_deleted, conflicts_detected, conflicts_resolved, error_message, error_details, sync_metadata, sync_config, throughput_records_per_second, memory_usage_peak_mb, network_bytes_transferred, is_automatic, is_retry, is_critical, retry_count, max_retries, next_retry_at, created_at, updated_at) VALUES ($1::INTEGER, $2::VARCHAR, $3::INTEGER, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::TIMESTAMP WITHOUT TIME ZONE, $8::TIMESTAMP WITHOUT TIME ZONE, $9::INTEGER, $10::VARCHAR, $11::VARCHAR, $12::INTEGER, $13::INTEGER, $14::INTEGER, $15::INTEGER, $16::INTEGER, $17::INTEGER, $18::VARCHAR, $19::VARCHAR, $20::VARCHAR, $21::VARCHAR, $22::FLOAT, $23::INTEGER, $24::INTEGER, $25::BOOLEAN, $26::BOOLEAN, $27::BOOLEAN, $28::INTEGER, $29::INTEGER, $30::TIMESTAMP WITHOUT TIME ZONE, $31::TIMESTAMP WITHOUT TIME ZONE, $32::TIMESTAMP WITHOUT TIME ZONE) RETURNING synchronization_logs.id]
[parameters: (1, None, None, 'Manual Sync', 'Local to Central', 'Completed', datetime.datetime(2025, 8, 11, 17, 32, 6, 211156), datetime.datetime(2025, 8, 11, 17, 47, 6, 211160), None, None, None, 150, 0, 0, 0, 0, 0, None, None, None, None, None, None, None, False, False, False, 0, 3, None, datetime.datetime(2025, 8, 11, 16, 32, 6, 211104, tzinfo=datetime.timezone.utc), datetime.datetime(2025, 8, 11, 16, 32, 6, 211104, tzinfo=datetime.timezone.utc))]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
