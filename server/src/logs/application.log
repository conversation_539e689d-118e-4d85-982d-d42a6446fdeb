2025-08-11 19:20:41.375 | INFO     | MainProcess:MainThread | src.config.logging_config:setup_logging:152 - Logging system configured and ready.
2025-08-11 19:20:41.376 | INFO     | MainProcess:MainThread | __main__:<module>:31 - Application starting up...
2025-08-11 19:20:41.378 | INFO     | MainProcess:MainThread | __main__:seed_general_data_command:140 - Attempting to seed general data...
2025-08-11 19:20:41.389 | INFO     | MainProcess:MainThread | logging:callHandlers:1744 - Starting database seeding for general models...
2025-08-11 19:20:41.390 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:367 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-08-11 19:20:41.390 | ERROR    | MainProcess:MainThread | src.core.errors.unified_error_handler:async_wrapper:653 - An unexpected error occurred in async_wrapper
Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/src/core/errors/unified_error_handler.py", line 684, in sync_wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/dev/ued/server/src/core/database/engine.py", line 154, in get_async_engine
    raise RuntimeError("Async database engine not initialized. Call initialize_database_engine() first.")
RuntimeError: Async database engine not initialized. Call initialize_database_engine() first.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/dev/ued/server/src/core/errors/unified_error_handler.py", line 647, in async_wrapper
    return await func(*args, **kwargs)
  File "/home/<USER>/dev/ued/server/src/core/database/session.py", line 41, in get_async_session_factory
    engine = get_async_engine()
  File "/home/<USER>/dev/ued/server/src/core/errors/unified_error_handler.py", line 709, in sync_wrapper
    raise DatabaseError(
src.core.errors.exceptions.DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
2025-08-11 19:20:41.400 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:367 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-08-11 19:20:41.402 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:147 - Error seeding general data: Database error: Database operation failed: An unexpected internal error occurred.
2025-08-11 19:21:12.874 | INFO     | MainProcess:MainThread | src.config.logging_config:setup_logging:152 - Logging system configured and ready.
2025-08-11 19:21:12.874 | INFO     | MainProcess:MainThread | __main__:<module>:31 - Application starting up...
2025-08-11 19:21:12.876 | INFO     | MainProcess:MainThread | __main__:seed_general_data_command:140 - Attempting to seed general data...
2025-08-11 19:21:12.878 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:43 - Starting database initialization...
2025-08-11 19:21:12.878 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:47 - Creating synchronous database engine for: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:21:12.965 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:63 - Successfully created synchronous engine for: PostgreSQL
2025-08-11 19:21:12.965 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:47 - Database engine created successfully
2025-08-11 19:21:12.965 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:51 - Session factory initialized
2025-08-11 19:21:12.967 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:96 - Running Alembic migrations...
2025-08-11 19:21:12.998 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:98 - Alembic migrations completed
2025-08-11 19:21:12.999 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:56 - Database migrations completed successfully
2025-08-11 19:21:13.000 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:68 - Database initialization completed successfully
2025-08-11 19:21:13.001 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:154 - Error seeding general data: object Engine can't be used in 'await' expression
2025-08-11 19:22:16.556 | INFO     | MainProcess:MainThread | src.config.logging_config:setup_logging:152 - Logging system configured and ready.
2025-08-11 19:22:16.556 | INFO     | MainProcess:MainThread | __main__:<module>:31 - Application starting up...
2025-08-11 19:22:16.558 | INFO     | MainProcess:MainThread | __main__:seed_general_data_command:140 - Attempting to seed general data...
2025-08-11 19:22:16.560 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:43 - Starting database initialization...
2025-08-11 19:22:16.560 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:47 - Creating synchronous database engine for: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:22:16.584 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:63 - Successfully created synchronous engine for: PostgreSQL
2025-08-11 19:22:16.584 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:47 - Database engine created successfully
2025-08-11 19:22:16.584 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:51 - Session factory initialized
2025-08-11 19:22:16.586 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:96 - Running Alembic migrations...
2025-08-11 19:22:16.600 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:98 - Alembic migrations completed
2025-08-11 19:22:16.600 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:56 - Database migrations completed successfully
2025-08-11 19:22:16.602 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:68 - Database initialization completed successfully
2025-08-11 19:22:16.602 | INFO     | MainProcess:MainThread | src.core.database.engine:initialize_database_engine:97 - Attempting to connect to database: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:22:16.665 | INFO     | MainProcess:MainThread | src.core.database.engine:_create_and_test_async_engine:124 - Async engine created successfully for: PostgreSQL
2025-08-11 19:22:16.666 | INFO     | MainProcess:MainThread | src.core.database.engine:initialize_database_engine:99 - Successfully connected to the database.
2025-08-11 19:22:17.363 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:158 - Error seeding general data: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.NotNullViolationError'>: null value in column "start_date" of relation "project_phases" violates not-null constraint
DETAIL:  Failing row contains (685d86ae-e5af-4a06-ba55-4ef8e0e4f34c, 1, Planning, null, null, 100, t, null, null, null, 1, Project Planning, 2025-08-11 16:22:17.355739, 2025-08-11 16:22:17.355741, f, null, null).
[SQL: INSERT INTO project_phases (phase_id, project_id, phase_type, start_date, end_date, progress_percentage, is_active, notes, name, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) SELECT p0::UUID, p1::INTEGER, p2::VARCHAR, p3::TIMEST ... 2697 characters truncated ...  p12, p13, sen_counter) ORDER BY sen_counter RETURNING project_phases.id, project_phases.id AS id__1]
[parameters: ('685d86ae-e5af-4a06-ba55-4ef8e0e4f34c', 1, 'Planning', None, None, 100.0, True, None, 'Project Planning', datetime.datetime(2025, 8, 11, 16, 22, 17, 355739), datetime.datetime(2025, 8, 11, 16, 22, 17, 355741), False, None, None, 'fc25f087-dd1f-44f4-83d3-6b8055b40772', 1, 'Conceptual Design', None, None, 75.0, True, None, 'Conceptual Design', datetime.datetime(2025, 8, 11, 16, 22, 17, 355743), datetime.datetime(2025, 8, 11, 16, 22, 17, 355743), False, None, None, '0ba56a77-6dd1-4628-a20a-48017b0c6ae8', 1, 'Schematic Design', None, None, 25.0, True, None, 'Schematic Design', datetime.datetime(2025, 8, 11, 16, 22, 17, 355744), datetime.datetime(2025, 8, 11, 16, 22, 17, 355745), False, None, None, '26b2e126-000b-43bd-8aa8-413ede02ef14', 1, 'Design Development', None, None, 0.0, True, None ... 12 parameters truncated ... True, None, 'Project Planning', datetime.datetime(2025, 8, 11, 16, 22, 17, 355746), datetime.datetime(2025, 8, 11, 16, 22, 17, 355747), False, None, None, '5dc66e2a-eaf3-4e27-baf4-52a244df3bbc', 2, 'Conceptual Design', None, None, 0.0, True, None, 'Conceptual Design', datetime.datetime(2025, 8, 11, 16, 22, 17, 355747), datetime.datetime(2025, 8, 11, 16, 22, 17, 355747), False, None, None, '8693bb58-f931-40fe-bbe3-738dc929606d', 2, 'Schematic Design', None, None, 0.0, True, None, 'Schematic Design', datetime.datetime(2025, 8, 11, 16, 22, 17, 355748), datetime.datetime(2025, 8, 11, 16, 22, 17, 355748), False, None, None, '5be2f177-3c67-4acf-b770-6788d22e7fa8', 2, 'Design Development', None, None, 0.0, True, None, 'Design Development', datetime.datetime(2025, 8, 11, 16, 22, 17, 355749), datetime.datetime(2025, 8, 11, 16, 22, 17, 355749), False, None, None)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-11 19:23:14.117 | INFO     | MainProcess:MainThread | src.config.logging_config:setup_logging:152 - Logging system configured and ready.
2025-08-11 19:23:14.118 | INFO     | MainProcess:MainThread | __main__:<module>:31 - Application starting up...
2025-08-11 19:23:14.119 | INFO     | MainProcess:MainThread | __main__:seed_general_data_command:140 - Attempting to seed general data...
2025-08-11 19:23:14.127 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:43 - Starting database initialization...
2025-08-11 19:23:14.127 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:47 - Creating synchronous database engine for: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:23:14.153 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:63 - Successfully created synchronous engine for: PostgreSQL
2025-08-11 19:23:14.153 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:47 - Database engine created successfully
2025-08-11 19:23:14.154 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:51 - Session factory initialized
2025-08-11 19:23:14.155 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:96 - Running Alembic migrations...
2025-08-11 19:23:14.172 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:98 - Alembic migrations completed
2025-08-11 19:23:14.172 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:56 - Database migrations completed successfully
2025-08-11 19:23:14.174 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:68 - Database initialization completed successfully
2025-08-11 19:23:14.174 | INFO     | MainProcess:MainThread | src.core.database.engine:initialize_database_engine:97 - Attempting to connect to database: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:23:14.220 | INFO     | MainProcess:MainThread | src.core.database.engine:_create_and_test_async_engine:124 - Async engine created successfully for: PostgreSQL
2025-08-11 19:23:14.221 | INFO     | MainProcess:MainThread | src.core.database.engine:initialize_database_engine:99 - Successfully connected to the database.
2025-08-11 19:23:14.344 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:158 - Error seeding general data: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.NotNullViolationError'>: null value in column "title" of relation "project_milestones" violates not-null constraint
DETAIL:  Failing row contains (0264db1a-2e31-4026-bbaa-acb166edf152, 2, null, All project requirements documented, null, null, Completed, null, null, null, 1, Requirements Gathering Complete, null, 2025-08-11 16:23:14.339107, 2025-08-11 16:23:14.33911, f, null, null).
[SQL: INSERT INTO project_milestones (milestone_id, phase_id, title, description, due_date, completion_date, status, assigned_user_id, name, notes, created_at, updated_at, is_deleted, deleted_at, deleted_by_user_id) SELECT p0::UUID, p1::INTEGER, p2::VARCHA ... 5479 characters truncated ... 4, sen_counter) ORDER BY sen_counter RETURNING project_milestones.id, project_milestones.id AS id__1]
[parameters: ('0264db1a-2e31-4026-bbaa-acb166edf152', 2, None, 'All project requirements documented', None, None, 'Completed', None, 'Requirements Gathering Complete', None, datetime.datetime(2025, 8, 11, 16, 23, 14, 339107), datetime.datetime(2025, 8, 11, 16, 23, 14, 339110), False, None, None, '0f3bcfc0-9d09-4605-afa7-0beb74518eb5', 2, None, 'Project charter signed off by stakeholders', None, None, 'Completed', None, 'Project Charter Approved', None, datetime.datetime(2025, 8, 11, 16, 23, 14, 339111), datetime.datetime(2025, 8, 11, 16, 23, 14, 339111), False, None, None, '6075d4e7-3bcf-4c3b-b0cc-2b91e462f983', 3, None, 'Initial design concept reviewed and approved', None, None, 'Completed', None, 'Concept Design Review', None, datetime.datetime(2025, 8, 11, 16, 23, 14, 339112), datetime.datetime(2025, 8, 11, 16, 23, 14, 339112), False, None, None, '5b67a898-e65d-42d8-b5bd-55e83dece766', 3, None, 'Electrical load analysis completed', None ... 140 parameters truncated ... datetime.datetime(2025, 8, 11, 16, 23, 14, 339120), datetime.datetime(2025, 8, 11, 16, 23, 14, 339121), False, None, None, '853c0f35-8e64-4056-984d-0d35f897dce8', 8, None, 'Major equipment specifications defined', None, None, 'Planned', None, 'Equipment Specifications', None, datetime.datetime(2025, 8, 11, 16, 23, 14, 339121), datetime.datetime(2025, 8, 11, 16, 23, 14, 339122), False, None, None, '4d2f5a24-dc28-41d4-a0c6-0ce6daf7e3d4', 9, None, 'All detailed electrical drawings completed', None, None, 'Planned', None, 'Detailed Drawings Complete', None, datetime.datetime(2025, 8, 11, 16, 23, 14, 339122), datetime.datetime(2025, 8, 11, 16, 23, 14, 339122), False, None, None, 'ea8cee9b-315f-4c75-a8ea-c830c8f82dd8', 9, None, 'All panel schedules and layouts finalized', None, None, 'Planned', None, 'Panel Schedules Finalized', None, datetime.datetime(2025, 8, 11, 16, 23, 14, 339123), datetime.datetime(2025, 8, 11, 16, 23, 14, 339123), False, None, None)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-11 19:24:06.859 | INFO     | MainProcess:MainThread | src.config.logging_config:setup_logging:152 - Logging system configured and ready.
2025-08-11 19:24:06.859 | INFO     | MainProcess:MainThread | __main__:<module>:31 - Application starting up...
2025-08-11 19:24:06.861 | INFO     | MainProcess:MainThread | __main__:seed_general_data_command:140 - Attempting to seed general data...
2025-08-11 19:24:06.870 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:43 - Starting database initialization...
2025-08-11 19:24:06.870 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:47 - Creating synchronous database engine for: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:24:06.890 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:63 - Successfully created synchronous engine for: PostgreSQL
2025-08-11 19:24:06.890 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:47 - Database engine created successfully
2025-08-11 19:24:06.890 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:51 - Session factory initialized
2025-08-11 19:24:06.892 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:96 - Running Alembic migrations...
2025-08-11 19:24:06.909 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:98 - Alembic migrations completed
2025-08-11 19:24:06.909 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:56 - Database migrations completed successfully
2025-08-11 19:24:06.911 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:68 - Database initialization completed successfully
2025-08-11 19:24:06.911 | INFO     | MainProcess:MainThread | src.core.database.engine:initialize_database_engine:97 - Attempting to connect to database: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:24:06.952 | INFO     | MainProcess:MainThread | src.core.database.engine:_create_and_test_async_engine:124 - Async engine created successfully for: PostgreSQL
2025-08-11 19:24:06.952 | INFO     | MainProcess:MainThread | src.core.database.engine:initialize_database_engine:99 - Successfully connected to the database.
2025-08-11 19:24:07.104 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:158 - Error seeding general data: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedTableError'>: relation "system_configurations" does not exist
[SQL: SELECT system_configurations.config_id 
FROM system_configurations]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-11 19:30:36.608 | INFO     | MainProcess:MainThread | src.config.logging_config:setup_logging:152 - Logging system configured and ready.
2025-08-11 19:30:36.608 | INFO     | MainProcess:MainThread | __main__:<module>:31 - Application starting up...
2025-08-11 19:30:36.609 | INFO     | MainProcess:MainThread | __main__:seed_general_data_command:140 - Attempting to seed general data...
2025-08-11 19:30:36.612 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:43 - Starting database initialization...
2025-08-11 19:30:36.613 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:47 - Creating synchronous database engine for: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:30:36.643 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:63 - Successfully created synchronous engine for: PostgreSQL
2025-08-11 19:30:36.643 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:47 - Database engine created successfully
2025-08-11 19:30:36.643 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:51 - Session factory initialized
2025-08-11 19:30:36.645 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:96 - Running Alembic migrations...
2025-08-11 19:30:36.662 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:98 - Alembic migrations completed
2025-08-11 19:30:36.662 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:56 - Database migrations completed successfully
2025-08-11 19:30:36.664 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:68 - Database initialization completed successfully
2025-08-11 19:30:36.665 | INFO     | MainProcess:MainThread | src.core.database.engine:initialize_database_engine:97 - Attempting to connect to database: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:30:36.716 | INFO     | MainProcess:MainThread | src.core.database.engine:_create_and_test_async_engine:124 - Async engine created successfully for: PostgreSQL
2025-08-11 19:30:36.717 | INFO     | MainProcess:MainThread | src.core.database.engine:initialize_database_engine:99 - Successfully connected to the database.
2025-08-11 19:30:36.837 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:367 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-08-11 19:31:26.550 | INFO     | MainProcess:MainThread | src.config.logging_config:setup_logging:152 - Logging system configured and ready.
2025-08-11 19:31:26.550 | INFO     | MainProcess:MainThread | __main__:<module>:31 - Application starting up...
2025-08-11 19:31:26.551 | INFO     | MainProcess:MainThread | __main__:seed_general_data_command:140 - Attempting to seed general data...
2025-08-11 19:31:26.560 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:43 - Starting database initialization...
2025-08-11 19:31:26.560 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:47 - Creating synchronous database engine for: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:31:26.582 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:63 - Successfully created synchronous engine for: PostgreSQL
2025-08-11 19:31:26.582 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:47 - Database engine created successfully
2025-08-11 19:31:26.582 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:51 - Session factory initialized
2025-08-11 19:31:26.583 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:96 - Running Alembic migrations...
2025-08-11 19:31:26.597 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:98 - Alembic migrations completed
2025-08-11 19:31:26.597 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:56 - Database migrations completed successfully
2025-08-11 19:31:26.599 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:68 - Database initialization completed successfully
2025-08-11 19:31:26.599 | INFO     | MainProcess:MainThread | src.core.database.engine:initialize_database_engine:97 - Attempting to connect to database: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:31:26.635 | INFO     | MainProcess:MainThread | src.core.database.engine:_create_and_test_async_engine:124 - Async engine created successfully for: PostgreSQL
2025-08-11 19:31:26.635 | INFO     | MainProcess:MainThread | src.core.database.engine:initialize_database_engine:99 - Successfully connected to the database.
2025-08-11 19:31:26.754 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:367 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-08-11 19:32:06.010 | INFO     | MainProcess:MainThread | src.config.logging_config:setup_logging:152 - Logging system configured and ready.
2025-08-11 19:32:06.010 | INFO     | MainProcess:MainThread | __main__:<module>:31 - Application starting up...
2025-08-11 19:32:06.011 | INFO     | MainProcess:MainThread | __main__:seed_general_data_command:140 - Attempting to seed general data...
2025-08-11 19:32:06.020 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:43 - Starting database initialization...
2025-08-11 19:32:06.020 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:47 - Creating synchronous database engine for: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:32:06.042 | INFO     | MainProcess:MainThread | src.core.database.engine:create_engine:63 - Successfully created synchronous engine for: PostgreSQL
2025-08-11 19:32:06.042 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:47 - Database engine created successfully
2025-08-11 19:32:06.043 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:51 - Session factory initialized
2025-08-11 19:32:06.044 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:96 - Running Alembic migrations...
2025-08-11 19:32:06.057 | INFO     | MainProcess:MainThread | src.core.database.initialization:run_alembic_migrations:98 - Alembic migrations completed
2025-08-11 19:32:06.057 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:56 - Database migrations completed successfully
2025-08-11 19:32:06.058 | INFO     | MainProcess:MainThread | src.core.database.initialization:initialize_database:68 - Database initialization completed successfully
2025-08-11 19:32:06.059 | INFO     | MainProcess:MainThread | src.core.database.engine:initialize_database_engine:97 - Attempting to connect to database: postgresql://user:password@localhost:5432/ultimate_electrical_designer
2025-08-11 19:32:06.096 | INFO     | MainProcess:MainThread | src.core.database.engine:_create_and_test_async_engine:124 - Async engine created successfully for: PostgreSQL
2025-08-11 19:32:06.096 | INFO     | MainProcess:MainThread | src.core.database.engine:initialize_database_engine:99 - Successfully connected to the database.
2025-08-11 19:32:06.214 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:158 - Error seeding general data: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.DataError'>: invalid input for query argument $31: datetime.datetime(2025, 8, 11, 16, 32, 6... (can't subtract offset-naive and offset-aware datetimes)
[SQL: INSERT INTO synchronization_logs (project_id, session_id, user_id, operation_type, sync_direction, status, started_at, completed_at, duration_ms, source_database_url, target_database_url, records_processed, records_created, records_updated, records_deleted, conflicts_detected, conflicts_resolved, error_message, error_details, sync_metadata, sync_config, throughput_records_per_second, memory_usage_peak_mb, network_bytes_transferred, is_automatic, is_retry, is_critical, retry_count, max_retries, next_retry_at, created_at, updated_at) VALUES ($1::INTEGER, $2::VARCHAR, $3::INTEGER, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::TIMESTAMP WITHOUT TIME ZONE, $8::TIMESTAMP WITHOUT TIME ZONE, $9::INTEGER, $10::VARCHAR, $11::VARCHAR, $12::INTEGER, $13::INTEGER, $14::INTEGER, $15::INTEGER, $16::INTEGER, $17::INTEGER, $18::VARCHAR, $19::VARCHAR, $20::VARCHAR, $21::VARCHAR, $22::FLOAT, $23::INTEGER, $24::INTEGER, $25::BOOLEAN, $26::BOOLEAN, $27::BOOLEAN, $28::INTEGER, $29::INTEGER, $30::TIMESTAMP WITHOUT TIME ZONE, $31::TIMESTAMP WITHOUT TIME ZONE, $32::TIMESTAMP WITHOUT TIME ZONE) RETURNING synchronization_logs.id]
[parameters: (1, None, None, 'Manual Sync', 'Local to Central', 'Completed', datetime.datetime(2025, 8, 11, 17, 32, 6, 211156), datetime.datetime(2025, 8, 11, 17, 47, 6, 211160), None, None, None, 150, 0, 0, 0, 0, 0, None, None, None, None, None, None, None, False, False, False, 0, 3, None, datetime.datetime(2025, 8, 11, 16, 32, 6, 211104, tzinfo=datetime.timezone.utc), datetime.datetime(2025, 8, 11, 16, 32, 6, 211104, tzinfo=datetime.timezone.utc))]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
