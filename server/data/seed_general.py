"""
This script, `seed_general.py`, provides a comprehensive and idempotent mechanism
for seeding the Ultimate Electrical Designer's database with foundational data
across various general models. It ensures a consistent and ready-to-use
development environment by populating essential entities such as user roles,
user accounts, project data, and the core electrical component catalog.

The seeding process is designed to be idempotent, meaning it can be executed
multiple times without creating duplicate records, making it safe for
repeated runs during development or testing phases. It adheres to a strict
order of operations to satisfy data dependencies (e.g., roles are seeded
before users, and component infrastructure before individual components).

Key functions include:
- `seed_roles`: Populates default user roles (<PERSON><PERSON>, Engineer, Viewer).
- `seed_permissions`: Creates system permissions for RBAC.
- `seed_role_permissions`: Assigns permissions to roles.
- `seed_users`: Creates sample user accounts with assigned roles and preferences.
- `seed_projects_and_members`: Seeds sample projects and assigns users as members.
- `seed_project_phases`: Creates project phases for sample projects.
- `seed_project_milestones`: Creates milestones for project phases.
- `seed_project_templates`: Creates reusable project templates.
- `seed_component_infrastructure`: Establishes the foundational component
  classification system by seeding `ComponentCategory` and `ComponentType`
  records based on defined enums and mappings.
- `seed_components`: Generates one dummy `Component` entry for each
  `ComponentType` enum member, providing initial catalog data.
- `seed_tasks`: Creates sample tasks and task assignments.
- `seed_system_configurations`: Creates system configuration entries.
- `seed_synchronization_logs`: Creates sample synchronization logs and conflicts.
- `seed_logs`: Adds sample activity and audit trail logs for system monitoring
  and history.

This script is critical for setting up a functional local development
or testing environment quickly and reliably, aligning with the project's
emphasis on engineering-grade quality and structured data management.
"""

import asyncio
import logging
import uuid
from datetime import date, datetime, timezone, timedelta
from typing import Any, Dict, List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.config.settings import settings
from src.core.database.session import get_async_db_session
from src.core.enums.electrical_enums import (
    COMPONENT_TYPE_TO_CATEGORY_MAPPING,
    ComponentCategoryType,
    ComponentType as ComponentTypeEnum,
)
from src.core.enums.project_management_enums import (
    ProjectPhaseType,
    MilestoneStatus,
    ProjectTemplateType,
    TaskPriority,
    TaskStatus,
)
from src.core.enums.common_enums import FrequencyType, TemperatureUnit
from src.core.enums.system_enums import (
    ErrorSeverity,
    SyncDirection,
    SyncOperation,
    SyncStatus,
)
from src.core.models.general.activity_log import ActivityLog, AuditTrail
from src.core.models.general.component import Component
from src.core.models.general.component_category import ComponentCategory
from src.core.models.general.component_type import ComponentType
from src.core.models.general.permission import Permission, RolePermission
from src.core.models.general.project import Project, ProjectMember
from src.core.models.general.project_phase import ProjectPhase, ProjectMilestone, ProjectTemplate
from src.core.models.general.synchronization_log import SynchronizationLog, SynchronizationConflict
from src.core.models.general.system_configuration import SystemConfiguration
from src.core.models.general.task import Task, TaskAssignment
from src.core.models.general.user import User, UserPreference
from src.core.models.general.user_role import UserRole, UserRoleAssignment
from src.core.security.password_handler import PasswordHandler

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def seed_roles(db: AsyncSession) -> None:
    """Seeds the UserRole table if they don't already exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking roles...")
    existing_roles_result = await db.execute(select(UserRole.name))
    existing_roles = set(existing_roles_result.scalars().all())

    roles_to_add = [
        UserRole(name="Admin", description="Administrator with full access"),  # type: ignore
        UserRole(name="Engineer", description="Engineer with project access"),  # type: ignore
        UserRole(name="Viewer", description="Viewer with read-only access"),  # type: ignore
    ]

    new_roles = [role for role in roles_to_add if role.name not in existing_roles]
    if new_roles:
        db.add_all(new_roles)
        await db.commit()
        logger.info(f"Seeded {len(new_roles)} new roles.")
    else:
        logger.info("Roles already seeded.")


async def seed_permissions(db: AsyncSession) -> None:
    """Seeds the Permission table with system permissions if they don't already exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking permissions...")
    existing_permissions_result = await db.execute(select(Permission.name))
    existing_permissions = set(existing_permissions_result.scalars().all())

    permissions_to_add = [
        Permission(name="project_read", resource="project", action="read", description="Read project data"),  # type: ignore
        Permission(name="project_write", resource="project", action="write", description="Create and update projects"),  # type: ignore
        Permission(name="project_delete", resource="project", action="delete", description="Delete projects"),  # type: ignore
        Permission(name="component_read", resource="component", action="read", description="Read component data"),  # type: ignore
        Permission(
            name="component_write", resource="component", action="write", description="Create and update components"
        ),  # type: ignore
        Permission(name="component_delete", resource="component", action="delete", description="Delete components"),  # type: ignore
        Permission(name="user_read", resource="user", action="read", description="Read user data"),  # type: ignore
        Permission(name="user_write", resource="user", action="write", description="Create and update users"),  # type: ignore
        Permission(name="user_delete", resource="user", action="delete", description="Delete users"),  # type: ignore
        Permission(name="task_read", resource="task", action="read", description="Read task data"),  # type: ignore
        Permission(name="task_write", resource="task", action="write", description="Create and update tasks"),  # type: ignore
        Permission(name="task_delete", resource="task", action="delete", description="Delete tasks"),  # type: ignore
        Permission(name="system_admin", resource="system", action="admin", description="Full system administration"),  # type: ignore
    ]

    new_permissions = [perm for perm in permissions_to_add if perm.name not in existing_permissions]
    if new_permissions:
        db.add_all(new_permissions)
        await db.commit()
        logger.info(f"Seeded {len(new_permissions)} new permissions.")
    else:
        logger.info("Permissions already seeded.")


async def seed_role_permissions(db: AsyncSession) -> None:
    """Seeds role-permission assignments if they don't already exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking role permissions...")

    # Get all roles and permissions
    roles_result = await db.execute(select(UserRole))
    roles = {role.name: role for role in roles_result.scalars().all()}

    permissions_result = await db.execute(select(Permission))
    permissions = {perm.name: perm for perm in permissions_result.scalars().all()}

    # Check existing role permissions
    existing_rp_result = await db.execute(select(RolePermission.role_id, RolePermission.permission_id))
    existing_role_permissions = set(existing_rp_result.all())

    # Define role-permission mappings
    role_permission_mappings = {
        "Admin": [
            "project_read",
            "project_write",
            "project_delete",
            "component_read",
            "component_write",
            "component_delete",
            "user_read",
            "user_write",
            "user_delete",
            "task_read",
            "task_write",
            "task_delete",
            "system_admin",
        ],
        "Engineer": ["project_read", "project_write", "component_read", "component_write", "task_read", "task_write"],
        "Viewer": ["project_read", "component_read", "task_read"],
    }

    role_permissions_to_add = []
    for role_name, permission_names in role_permission_mappings.items():
        role = roles.get(role_name)
        if not role:
            logger.warning(f"Role '{role_name}' not found, skipping permission assignments.")
            continue

        for permission_name in permission_names:
            permission = permissions.get(permission_name)
            if not permission:
                logger.warning(f"Permission '{permission_name}' not found, skipping.")
                continue

            if (role.id, permission.id) not in existing_role_permissions:
                role_permission = RolePermission()
                role_permission.role_id = role.id
                role_permission.permission_id = permission.id
                role_permission.is_active = True
                role_permission.grant_context = "Initial seeding"
                role_permissions_to_add.append(role_permission)

    if role_permissions_to_add:
        db.add_all(role_permissions_to_add)
        await db.commit()
        logger.info(f"Seeded {len(role_permissions_to_add)} new role-permission assignments.")
    else:
        logger.info("Role permissions already seeded.")


async def seed_users(db: AsyncSession) -> None:
    """Seeds users with various roles if they don't already exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking users...")

    users_to_seed: List[Dict[str, Any]] = [
        {
            "name": "admin",
            "email": "<EMAIL>",
            "password": settings.SECRET_KEY,
            "is_superuser": True,
            "role_name": "Admin",
        },
        {
            "name": "engineer",
            "email": "<EMAIL>",
            "password": "EngineerPassword123",
            "is_superuser": False,
            "role_name": "Engineer",
        },
        {
            "name": "viewer",
            "email": "<EMAIL>",
            "password": "ViewerPassword123",
            "is_superuser": False,
            "role_name": "Viewer",
        },
    ]

    for user_data in users_to_seed:
        user_result = await db.execute(select(User).filter_by(name=user_data["name"]))
        if user_result.scalars().first():
            logger.info(f"User '{user_data['name']}' already exists.")
            continue

        logger.info(f"Seeding user: {user_data['name']}")
        hashed_password = PasswordHandler.hash_password(str(user_data["password"]))
        new_user = User()
        new_user.name = user_data["name"]
        new_user.email = user_data["email"]
        new_user.password_hash = hashed_password
        new_user.is_active = True
        new_user.is_superuser = user_data["is_superuser"]
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)

        role_result = await db.execute(select(UserRole).filter_by(name=user_data["role_name"]))
        role = role_result.scalars().first()
        if role:
            assignment = UserRoleAssignment()
            assignment.user_id = new_user.id
            assignment.role_id = role.id
            assignment.name = f"{new_user.name}-{role.name} Assignment"
            db.add(assignment)

        preference = UserPreference()
        preference.user_id = new_user.id
        preference.name = f"{new_user.name}'s Preferences"
        preference.ui_theme = "light"
        db.add(preference)

        await db.commit()
        logger.info(f"User '{user_data['name']}' seeded successfully.")


async def seed_projects_and_members(db: AsyncSession) -> None:
    """Seeds sample projects and assigns members.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking for sample projects...")

    projects_to_seed: List[Dict[str, str]] = [
        {
            "name": "Industrial Plant Expansion",
            "project_number": "PROJ-001",
            "client": "Global Manufacturing Co.",
            "description": "Electrical design for the new manufacturing wing.",
        },
        {
            "name": "Commercial Building Upgrade",
            "project_number": "PROJ-002",
            "client": "City Properties LLC",
            "description": "Lighting and power system upgrade for a 10-story office building.",
        },
    ]

    users_result = await db.execute(select(User))
    users = {user.name: user for user in users_result.scalars().all()}

    roles_result = await db.execute(select(UserRole))
    roles = {role.name: role for role in roles_result.scalars().all()}

    for project_data in projects_to_seed:
        project_result = await db.execute(select(Project).filter_by(project_number=project_data["project_number"]))
        project = project_result.scalars().first()

        if not project:
            logger.info(f"Seeding project: {project_data['name']}")
            project = Project(  # type: ignore
                name=project_data["name"],
                project_number=project_data["project_number"],  # type: ignore
                client=project_data["client"],
                description=project_data["description"],  # type: ignore
                status="active",
                available_voltages_json='{"voltages": [230, 400, 480]}',  # type: ignore
            )
            db.add(project)
            await db.commit()
            await db.refresh(project)
            logger.info(f"Project '{project.name}' seeded.")

            if project.project_number == "PROJ-001":
                if users.get("admin") and roles.get("Admin"):
                    member = ProjectMember()
                    member.user_id = users["admin"].id
                    member.project_id = project.id
                    member.role_id = roles["Admin"].id
                    member.name = f"{users['admin'].name}-{project.name} Membership"
                    db.add(member)
                if users.get("engineer") and roles.get("Engineer"):
                    member = ProjectMember()
                    member.user_id = users["engineer"].id
                    member.project_id = project.id
                    member.role_id = roles["Engineer"].id
                    member.name = f"{users['engineer'].name}-{project.name} Membership"
                    db.add(member)
            elif project.project_number == "PROJ-002":
                if users.get("engineer") and roles.get("Engineer"):
                    member = ProjectMember()
                    member.user_id = users["engineer"].id
                    member.project_id = project.id
                    member.role_id = roles["Engineer"].id
                    member.name = f"{users['engineer'].name}-{project.name} Membership"
                    db.add(member)
                if users.get("viewer") and roles.get("Viewer"):
                    member = ProjectMember()
                    member.user_id = users["viewer"].id
                    member.project_id = project.id
                    member.role_id = roles["Viewer"].id
                    member.name = f"{users['viewer'].name}-{project.name} Membership"
                    db.add(member)

            await db.commit()
            logger.info(f"Members assigned to project '{project.name}'.")
        else:
            logger.info(f"Project '{project_data['name']}' already exists.")


async def seed_project_phases(db: AsyncSession) -> None:
    """Seeds project phases for existing projects if they don't already exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking project phases...")

    # Get all projects
    projects_result = await db.execute(select(Project))
    projects = list(projects_result.scalars().all())

    if not projects:
        logger.info("No projects found, skipping project phase seeding.")
        return

    # Check existing phases
    existing_phases_result = await db.execute(select(ProjectPhase.project_id, ProjectPhase.phase_type))
    existing_phases = set(existing_phases_result.all())

    phases_to_add = []
    for project in projects:
        # Define standard phases for electrical projects
        base_start_date = datetime.now() - timedelta(days=30)
        standard_phases = [
            {
                "phase_type": ProjectPhaseType.PLANNING,
                "name": "Project Planning",
                "description": "Initial project planning and requirements gathering",
                "order_index": 1,
                "progress_percentage": 100.0 if project.project_number == "PROJ-001" else 50.0,
                "start_date": base_start_date,
                "end_date": base_start_date + timedelta(days=14) if project.project_number == "PROJ-001" else None,
            },
            {
                "phase_type": ProjectPhaseType.CONCEPTUAL_DESIGN,
                "name": "Conceptual Design",
                "description": "High-level electrical system design and concept development",
                "order_index": 2,
                "progress_percentage": 75.0 if project.project_number == "PROJ-001" else 0.0,
                "start_date": base_start_date + timedelta(days=15),
                "end_date": base_start_date + timedelta(days=35) if project.project_number == "PROJ-001" else None,
            },
            {
                "phase_type": ProjectPhaseType.SCHEMATIC_DESIGN,
                "name": "Schematic Design",
                "description": "Detailed electrical schematics and single-line diagrams",
                "order_index": 3,
                "progress_percentage": 25.0 if project.project_number == "PROJ-001" else 0.0,
                "start_date": base_start_date + timedelta(days=36),
                "end_date": None,
            },
            {
                "phase_type": ProjectPhaseType.DESIGN_DEVELOPMENT,
                "name": "Design Development",
                "description": "Detailed design development and specifications",
                "order_index": 4,
                "progress_percentage": 0.0,
                "start_date": base_start_date + timedelta(days=60),
                "end_date": None,
            },
        ]

        for phase_data in standard_phases:
            if (project.id, phase_data["phase_type"]) not in existing_phases:
                phase = ProjectPhase()
                phase.phase_id = str(uuid.uuid4())
                phase.project_id = project.id
                phase.phase_type = phase_data["phase_type"]
                phase.name = phase_data["name"]
                phase.description = phase_data["description"]
                phase.order_index = phase_data["order_index"]
                phase.progress_percentage = phase_data["progress_percentage"]
                phase.start_date = phase_data["start_date"]
                phase.end_date = phase_data["end_date"]
                phase.is_active = True
                phases_to_add.append(phase)

    if phases_to_add:
        db.add_all(phases_to_add)
        await db.commit()
        logger.info(f"Seeded {len(phases_to_add)} new project phases.")
    else:
        logger.info("Project phases already seeded.")


async def seed_project_milestones(db: AsyncSession) -> None:
    """Seeds project milestones for existing project phases if they don't already exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking project milestones...")

    # Get all project phases
    phases_result = await db.execute(select(ProjectPhase))
    phases = list(phases_result.scalars().all())

    if not phases:
        logger.info("No project phases found, skipping milestone seeding.")
        return

    # Check existing milestones
    existing_milestones_result = await db.execute(select(ProjectMilestone.phase_id, ProjectMilestone.name))
    existing_milestones = set(existing_milestones_result.all())

    milestones_to_add = []
    for phase in phases:
        # Define milestones based on phase type
        milestone_templates = {
            ProjectPhaseType.PLANNING: [
                {"name": "Requirements Gathering Complete", "description": "All project requirements documented"},
                {"name": "Project Charter Approved", "description": "Project charter signed off by stakeholders"},
            ],
            ProjectPhaseType.CONCEPTUAL_DESIGN: [
                {"name": "Concept Design Review", "description": "Initial design concept reviewed and approved"},
                {"name": "Load Analysis Complete", "description": "Electrical load analysis completed"},
            ],
            ProjectPhaseType.SCHEMATIC_DESIGN: [
                {"name": "Single Line Diagrams Complete", "description": "All single line diagrams finalized"},
                {"name": "Equipment Specifications", "description": "Major equipment specifications defined"},
            ],
            ProjectPhaseType.DESIGN_DEVELOPMENT: [
                {"name": "Detailed Drawings Complete", "description": "All detailed electrical drawings completed"},
                {"name": "Panel Schedules Finalized", "description": "All panel schedules and layouts finalized"},
            ],
        }

        milestones = milestone_templates.get(phase.phase_type, [])
        for i, milestone_data in enumerate(milestones):
            if (phase.id, milestone_data["name"]) not in existing_milestones:
                milestone = ProjectMilestone()
                milestone.milestone_id = str(uuid.uuid4())
                milestone.phase_id = phase.id
                milestone.name = milestone_data["name"]
                milestone.title = milestone_data["name"]  # Set title field
                milestone.description = milestone_data["description"]
                milestone.order_index = i + 1
                milestone.status = (
                    MilestoneStatus.COMPLETED if phase.progress_percentage > 50 else MilestoneStatus.PLANNED
                )
                milestone.due_date = phase.start_date + timedelta(days=(i + 1) * 7)  # Set due date
                milestone.completion_date = (
                    milestone.due_date - timedelta(days=1) if milestone.status == MilestoneStatus.COMPLETED else None
                )
                milestone.is_critical = i == 0  # First milestone is critical
                milestones_to_add.append(milestone)

    if milestones_to_add:
        db.add_all(milestones_to_add)
        await db.commit()
        logger.info(f"Seeded {len(milestones_to_add)} new project milestones.")
    else:
        logger.info("Project milestones already seeded.")


async def seed_project_templates(db: AsyncSession) -> None:
    """Seeds project templates if they don't already exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking project templates...")

    existing_templates_result = await db.execute(select(ProjectTemplate.name))
    existing_templates = set(existing_templates_result.scalars().all())

    templates_to_add = [
        ProjectTemplate(  # type: ignore
            template_id=str(uuid.uuid4()),
            name="Industrial Power Distribution",
            template_type=ProjectTemplateType.INDUSTRIAL_POWER_DISTRIBUTION,
            category="industrial",
            description="Standard template for industrial power distribution projects",
            phases_config='{"phases": ["planning", "conceptual_design", "schematic_design", "design_development"]}',
            milestones_config='{"milestones": ["requirements_complete", "concept_approved", "schematics_complete"]}',
            default_settings='{"voltage_levels": [480, 208, 120], "frequency": 60}',
            compliance_standards='["IEEE 141", "NEC", "NFPA 70E"]',
            is_public=True,
        ),
        ProjectTemplate(  # type: ignore
            template_id=str(uuid.uuid4()),
            name="Commercial Building Electrical",
            template_type=ProjectTemplateType.BUILDING_ELECTRICAL,
            category="commercial",
            description="Standard template for commercial building electrical systems",
            phases_config='{"phases": ["planning", "schematic_design", "construction_documents"]}',
            milestones_config='{"milestones": ["load_analysis", "panel_schedules", "permit_submission"]}',
            default_settings='{"voltage_levels": [480, 277, 120], "frequency": 60}',
            compliance_standards='["NEC", "IBC", "Local Codes"]',
            is_public=True,
        ),
    ]

    new_templates = [template for template in templates_to_add if template.name not in existing_templates]
    if new_templates:
        db.add_all(new_templates)
        await db.commit()
        logger.info(f"Seeded {len(new_templates)} new project templates.")
    else:
        logger.info("Project templates already seeded.")


async def seed_components(db: AsyncSession) -> None:
    """Seeds one dummy component for each ComponentType enum member.

    This function ensures that every defined component type in the enum has at
    least one corresponding dummy component entry in the database. It is
    idempotent and will not create duplicates if a component for a given
    type already exists.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Preparing to seed dummy components for all component types...")

    # 1. Fetch existing component types and categories from DB
    types_result = await db.execute(select(ComponentType))
    type_map = {comp_type.name: comp_type for comp_type in types_result.scalars().all()}

    categories_result = await db.execute(select(ComponentCategory))
    category_map = {cat.name: cat for cat in categories_result.scalars().all()}

    # 2. Idempotence Check: Get all component_type_ids already in the Component table
    existing_components_result = await db.execute(select(Component.component_type_id).distinct())
    seeded_type_ids = set(existing_components_result.scalars().all())

    components_to_add = []
    # 3. Iterate through all ComponentType enums
    for component_type_enum in ComponentTypeEnum:
        # 4. Retrieve associated data from maps
        component_type_db = type_map.get(component_type_enum.value)

        if not component_type_db:
            logger.warning(
                f"ComponentType '{component_type_enum.value}' not found in the database. "
                "Skipping. Ensure seed_component_infrastructure has run."
            )
            continue

        # Idempotence check: if a component of this type exists, skip
        if component_type_db.id in seeded_type_ids:
            continue

        category_enum = COMPONENT_TYPE_TO_CATEGORY_MAPPING.get(component_type_enum)
        if not category_enum:
            logger.warning(f"No category mapping found for ComponentType '{component_type_enum.value}'. Skipping.")
            continue

        category_db = category_map.get(category_enum.value)
        if not category_db:
            logger.warning(
                f"ComponentCategory '{category_enum.value}' not found in the database for "
                f"ComponentType '{component_type_enum.value}'. Skipping."
            )
            continue

        # 5. Construct Dummy Component Object
        logger.info(f"Preparing dummy component for type: {component_type_enum.value}")
        dummy_component = Component(  # type: ignore
            name=f"Dummy {component_type_enum.value}",  # type: ignore
            manufacturer="Generic Corp",  # type: ignore
            model_number=f"DUMMY-{component_type_enum.name.replace('_', '-')}",  # type: ignore
            description=f"A generic, auto-seeded component for {component_type_enum.value}.",  # type: ignore
            component_type_id=component_type_db.id,  # type: ignore
            category_id=category_db.id,  # type: ignore
            specifications="{}",  # type: ignore
            unit_price=1.00,  # type: ignore
            currency="EUR",  # type: ignore
            is_active=True,  # type: ignore
        )
        components_to_add.append(dummy_component)

    # 6. Batch Insertion
    if components_to_add:
        logger.info(f"Adding {len(components_to_add)} new dummy components to the session...")
        db.add_all(components_to_add)
        await db.commit()
        logger.info("Batch commit complete. Dummy components seeded successfully.")
    else:
        logger.info("All component types already have at least one dummy component seeded.")


async def seed_tasks(db: AsyncSession) -> None:
    """Seeds sample tasks and task assignments if they don't already exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking tasks...")

    # Get projects and users
    projects_result = await db.execute(select(Project))
    projects = {project.project_number: project for project in projects_result.scalars().all()}

    users_result = await db.execute(select(User))
    users = {user.name: user for user in users_result.scalars().all()}

    if not projects or not users:
        logger.info("No projects or users found, skipping task seeding.")
        return

    # Check existing tasks
    existing_tasks_result = await db.execute(select(Task.title))
    existing_tasks = set(existing_tasks_result.scalars().all())

    tasks_to_add = []
    task_assignments_to_add = []

    # Define sample tasks for each project
    project_tasks = {
        "PROJ-001": [
            {
                "title": "Complete Load Analysis",
                "description": "Perform comprehensive electrical load analysis for the manufacturing wing",
                "priority": TaskPriority.HIGH,
                "status": TaskStatus.COMPLETED,
                "assigned_users": ["engineer"],
                "due_date": datetime.now() - timedelta(days=5),
            },
            {
                "title": "Design Single Line Diagrams",
                "description": "Create single line diagrams for main distribution panels",
                "priority": TaskPriority.HIGH,
                "status": TaskStatus.IN_PROGRESS,
                "assigned_users": ["engineer"],
                "due_date": datetime.now() + timedelta(days=10),
            },
            {
                "title": "Equipment Specification Review",
                "description": "Review and approve major electrical equipment specifications",
                "priority": TaskPriority.MEDIUM,
                "status": TaskStatus.NOT_STARTED,
                "assigned_users": ["admin", "engineer"],
                "due_date": datetime.now() + timedelta(days=20),
            },
        ],
        "PROJ-002": [
            {
                "title": "Lighting Layout Design",
                "description": "Design lighting layouts for all floors of the office building",
                "priority": TaskPriority.MEDIUM,
                "status": TaskStatus.IN_PROGRESS,
                "assigned_users": ["engineer"],
                "due_date": datetime.now() + timedelta(days=15),
            },
            {
                "title": "Power Distribution Planning",
                "description": "Plan power distribution system for the building upgrade",
                "priority": TaskPriority.HIGH,
                "status": TaskStatus.NOT_STARTED,
                "assigned_users": ["engineer"],
                "due_date": datetime.now() + timedelta(days=25),
            },
        ],
    }

    for project_number, task_list in project_tasks.items():
        project = projects.get(project_number)
        if not project:
            continue

        for task_data in task_list:
            if task_data["title"] not in existing_tasks:
                task = Task()
                task.task_id = str(uuid.uuid4())
                task.project_id = project.id
                task.title = task_data["title"]
                task.name = task_data["title"]  # Set name for CommonColumns compatibility
                task.description = task_data["description"]
                task.priority = task_data["priority"]
                task.status = task_data["status"]
                task.due_date = task_data["due_date"]
                tasks_to_add.append(task)

                # Create task assignments
                for user_name in task_data["assigned_users"]:
                    user = users.get(user_name)
                    if user:
                        assignment = TaskAssignment()
                        assignment.name = f"{task.title} - {user.name} Assignment"
                        assignment.user_id = user.id
                        assignment.is_active = True
                        # We'll set task_id after the task is committed
                        task_assignments_to_add.append((assignment, task, user))

    if tasks_to_add:
        db.add_all(tasks_to_add)
        await db.commit()

        # Refresh tasks to get their IDs and create assignments
        for assignment_data in task_assignments_to_add:
            assignment, task, user = assignment_data
            assignment.task_id = task.id
            db.add(assignment)

        await db.commit()
        logger.info(f"Seeded {len(tasks_to_add)} new tasks and {len(task_assignments_to_add)} task assignments.")
    else:
        logger.info("Tasks already seeded.")


async def seed_system_configurations(db: AsyncSession) -> None:
    """Seeds system configuration entries if they don't already exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking system configurations...")

    existing_configs_result = await db.execute(select(SystemConfiguration.config_id))
    existing_configs = set(existing_configs_result.scalars().all())

    configs_to_add = []

    # Global system configuration
    global_config_id = str(uuid.uuid4())
    if global_config_id not in existing_configs:
        global_config = SystemConfiguration()
        global_config.config_id = global_config_id
        global_config.name = "Global System Configuration"
        global_config.scope = "global"
        global_config.category = "system"
        global_config.voltage_system = "480V/277V 3-Phase 4-Wire"
        global_config.frequency_system = FrequencyType.HZ_60
        global_config.temperature_unit = TemperatureUnit.CELSIUS
        global_config.calculation_precision = 2
        global_config.rounding_method = "round_half_up"
        global_config.safety_factors = '{"motor": 1.25, "lighting": 1.0, "receptacle": 1.25}'
        global_config.environmental_conditions = '{"ambient_temp": 25, "altitude": 0, "humidity": 50}'
        global_config.is_active = True
        configs_to_add.append(global_config)

    # Project-specific configurations
    projects_result = await db.execute(select(Project))
    projects = list(projects_result.scalars().all())

    for project in projects:
        project_config_id = str(uuid.uuid4())
        if project_config_id not in existing_configs:
            project_config = SystemConfiguration()
            project_config.config_id = project_config_id
            project_config.project_id = project.id
            project_config.name = f"{project.name} Configuration"
            project_config.scope = "project"
            project_config.category = "calculation"
            project_config.voltage_system = "480V/277V 3-Phase 4-Wire"
            project_config.frequency_system = FrequencyType.HZ_60
            project_config.temperature_unit = TemperatureUnit.CELSIUS
            project_config.calculation_precision = 3
            project_config.rounding_method = "round_half_up"
            project_config.safety_factors = '{"motor": 1.25, "lighting": 1.0, "receptacle": 1.25, "hvac": 1.15}'
            project_config.environmental_conditions = '{"ambient_temp": 30, "altitude": 100, "humidity": 60}'
            project_config.is_active = True
            configs_to_add.append(project_config)

    if configs_to_add:
        db.add_all(configs_to_add)
        await db.commit()
        logger.info(f"Seeded {len(configs_to_add)} new system configurations.")
    else:
        logger.info("System configurations already seeded.")


async def seed_synchronization_logs(db: AsyncSession) -> None:
    """Seeds sample synchronization logs and conflicts if they don't already exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking synchronization logs...")

    # Check if any sync logs exist
    existing_logs_result = await db.execute(select(SynchronizationLog.id))
    existing_logs = list(existing_logs_result.scalars().all())

    if existing_logs:
        logger.info("Synchronization logs already exist.")
        return

    # Get projects and users
    projects_result = await db.execute(select(Project))
    projects = list(projects_result.scalars().all())

    users_result = await db.execute(select(User))
    users = list(users_result.scalars().all())

    if not projects or not users:
        logger.info("No projects or users found, skipping sync log seeding.")
        return

    admin_user = next((user for user in users if user.name == "admin"), users[0])
    project = projects[0]

    # Create sample sync log
    sync_log = SynchronizationLog()
    sync_log.sync_id = str(uuid.uuid4())
    sync_log.project_id = project.id
    sync_log.initiated_by_user_id = admin_user.id
    sync_log.operation_type = SyncOperation.MANUAL_SYNC
    sync_log.sync_direction = SyncDirection.LOCAL_TO_CENTRAL
    sync_log.status = SyncStatus.COMPLETED
    sync_log.name = f"Manual sync - {project.name}"
    sync_log.started_at = datetime.now() - timedelta(hours=2)
    sync_log.completed_at = datetime.now() - timedelta(hours=1, minutes=45)
    sync_log.records_processed = 150
    sync_log.records_successful = 148
    sync_log.records_failed = 2
    sync_log.error_summary = "2 records failed due to validation errors"
    sync_log.performance_metrics = '{"duration_seconds": 900, "throughput_records_per_second": 0.17}'

    db.add(sync_log)
    await db.commit()
    await db.refresh(sync_log)

    # Create sample sync conflict
    conflict = SynchronizationConflict()
    conflict.sync_log_id = sync_log.id
    conflict.entity_type = "component"
    conflict.entity_id = "comp_123"
    conflict.table_name = "components"
    conflict.conflict_type = "field_conflict"
    conflict.field_name = "unit_price"
    conflict.local_value = "125.50"
    conflict.central_value = "130.00"
    conflict.severity = ErrorSeverity.MEDIUM
    conflict.is_resolved = True
    conflict.resolution_strategy = "use_central"
    conflict.resolved_by_user_id = admin_user.id
    conflict.resolved_at = datetime.now() - timedelta(hours=1, minutes=30)
    conflict.resolution_notes = "Used central value as it reflects latest pricing update"

    db.add(conflict)
    await db.commit()

    logger.info("Seeded 1 synchronization log and 1 conflict.")


async def seed_logs(db: AsyncSession) -> None:
    """Seeds a variety of sample activity and audit trail logs.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking for sample logs...")

    log_check = await db.execute(select(ActivityLog).filter_by(action_type="USER_LOGIN"))
    if log_check.scalars().first():
        logger.info("Sample logs already exist.")
        return

    logger.info("Seeding sample logs...")

    users_result = await db.execute(select(User))
    users = {user.name: user for user in users_result.scalars().all()}

    projects_result = await db.execute(select(Project))
    projects = list(projects_result.scalars().all())

    if not users or not projects:
        logger.warning("Users or projects not found, skipping log seeding.")
        return

    admin = users.get("admin")
    engineer = users.get("engineer")
    project1 = projects[0]

    if not admin:
        logger.warning("Admin user not found, cannot seed logs.")
        return

    login_activity = ActivityLog()
    login_activity.user_id = admin.id
    login_activity.action_type = "USER_LOGIN"
    login_activity.name = f"{admin.name} - USER_LOGIN"
    login_activity.action_description = f"User '{admin.name}' logged in successfully."
    login_activity.status = "SUCCESS"
    login_activity.request_ip = "127.0.0.1"
    login_activity.is_security_related = True
    db.add(login_activity)
    await db.commit()
    await db.refresh(login_activity)

    project_creation_audit = AuditTrail()
    project_creation_audit.activity_log_id = login_activity.id
    project_creation_audit.user_id = admin.id
    project_creation_audit.table_name = "Project"
    project_creation_audit.record_id = project1.id
    project_creation_audit.operation = "INSERT"
    project_creation_audit.name = f"Project - INSERT - {project1.id}"
    project_creation_audit.new_value = f"Project '{project1.name}' created."
    project_creation_audit.change_reason = "Initial seeding"
    db.add(project_creation_audit)

    component_result = await db.execute(select(Component).filter_by(model_number="DUMMY-CABLE_POWER"))
    component = component_result.scalars().first()
    if component and engineer:
        update_activity = ActivityLog()
        update_activity.user_id = engineer.id
        update_activity.action_type = "COMPONENT_UPDATE"
        update_activity.name = f"{engineer.name} - COMPONENT_UPDATE"
        update_activity.action_description = f"Component '{component.name}' updated."
        update_activity.target_id = component.id
        update_activity.target_type = "Component"
        update_activity.status = "SUCCESS"
        db.add(update_activity)
        await db.commit()
        await db.refresh(update_activity)

        update_audit = AuditTrail()
        update_audit.activity_log_id = update_activity.id
        update_audit.user_id = engineer.id
        update_audit.table_name = "Component"
        update_audit.record_id = component.id
        update_audit.operation = "UPDATE"
        update_audit.name = f"Component - UPDATE - {component.id}"
        update_audit.field_name = "description"
        update_audit.old_value = component.description
        update_audit.new_value = "4-core XLPE insulated power cable (updated spec)."
        update_audit.change_reason = "Specification update"
        db.add(update_audit)

    await db.commit()
    logger.info("Sample logs seeded.")


async def seed_component_infrastructure(db: AsyncSession) -> None:
    """Seeds component categories and types from enums if they don't exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Seeding component infrastructure...")

    existing_categories_result = await db.execute(select(ComponentCategory.name))
    existing_categories = set(existing_categories_result.scalars().all())

    categories_to_add = [
        ComponentCategory(name=cat_enum.value, description=f"Category for {cat_enum.value}")  # type: ignore
        for cat_enum in ComponentCategoryType
        if cat_enum.value not in existing_categories
    ]

    if categories_to_add:
        db.add_all(categories_to_add)
        await db.commit()
        logger.info(f"Seeded {len(categories_to_add)} new component categories.")

    all_categories_result = await db.execute(select(ComponentCategory))
    category_map = {cat.name: cat for cat in all_categories_result.scalars().all()}

    existing_types_result = await db.execute(select(ComponentType.name, ComponentType.category_id))
    existing_types = {(name, cat_id) for name, cat_id in existing_types_result.all()}

    types_to_add = []
    for component_enum, category_enum in COMPONENT_TYPE_TO_CATEGORY_MAPPING.items():
        category_obj = category_map.get(category_enum.value)
        if not category_obj:
            logger.warning(
                f"Category '{category_enum.value}' not found. Skipping component type '{component_enum.value}'."
            )
            continue

        if (component_enum.value, category_obj.id) not in existing_types:
            types_to_add.append(
                ComponentType(  # type: ignore
                    name=component_enum.value,  # type: ignore
                    description=f"Component type for {component_enum.value}",  # type: ignore
                    category_id=category_obj.id,  # type: ignore
                )
            )

    if types_to_add:
        db.add_all(types_to_add)
        await db.commit()
        logger.info(f"Seeded {len(types_to_add)} new component types.")

    if not categories_to_add and not types_to_add:
        logger.info("Component infrastructure already seeded.")


async def seed_general_data() -> None:
    """Main function to run all seeding operations in proper dependency order."""
    logger.info("Starting database seeding for general models...")
    async with get_async_db_session() as db:
        # Phase 1: Basic entities with no dependencies
        await seed_roles(db)
        await seed_permissions(db)

        # Phase 2: User-related entities
        await seed_users(db)
        await seed_role_permissions(db)

        # Phase 3: Component infrastructure
        await seed_component_infrastructure(db)
        await seed_components(db)

        # Phase 4: Project-related entities
        await seed_projects_and_members(db)
        await seed_project_phases(db)
        await seed_project_milestones(db)
        await seed_project_templates(db)

        # Phase 5: Task management
        await seed_tasks(db)

        # Phase 6: System configuration and synchronization
        await seed_system_configurations(db)
        await seed_synchronization_logs(db)

        # Phase 7: Activity logging (depends on most other entities)
        await seed_logs(db)

    logger.info("Database seeding completed successfully.")


if __name__ == "__main__":
    # It's recommended to run this script using `python -m src.data.seed_general`
    # from the `server` directory to ensure correct module resolution.
    asyncio.run(seed_general_data())
