# Seed General Data Documentation

## Overview

The `seed_general.py` script provides comprehensive database seeding functionality for the Ultimate Electrical Designer
application. It populates the database with foundational data across all general models, ensuring a consistent and
ready-to-use development environment.

## Purpose

This seeding script is designed to:

- Create essential foundational data for development and testing
- Provide realistic sample data that demonstrates the application's capabilities
- Ensure proper relationships and dependencies between models
- Support idempotent operations (can be run multiple times safely)

## Script Structure

### Main Function

- `seed_general_data()`: Orchestrates all seeding operations in proper dependency order

### Seeding Methods

#### Phase 1: Basic Entities (No Dependencies)

- `seed_roles()`: Creates default user roles (<PERSON><PERSON>, Engineer, Viewer)
- `seed_permissions()`: Creates system permissions for RBAC

#### Phase 2: User-Related Entities

- `seed_users()`: Creates sample user accounts with preferences
- `seed_role_permissions()`: Assigns permissions to roles

#### Phase 3: Component Infrastructure

- `seed_component_infrastructure()`: Creates component categories and types
- `seed_components()`: Creates sample components for each type

#### Phase 4: Project-Related Entities

- `seed_projects_and_members()`: Creates sample projects and assigns members
- `seed_project_phases()`: Creates project phases for electrical projects
- `seed_project_milestones()`: Creates milestones for project phases
- `seed_project_templates()`: Creates reusable project templates

#### Phase 5: Task Management

- `seed_tasks()`: Creates sample tasks and task assignments

#### Phase 6: System Configuration and Synchronization

- `seed_system_configurations()`: Creates system configuration entries
- `seed_synchronization_logs()`: Creates sample synchronization logs and conflicts

#### Phase 7: Activity Logging

- `seed_logs()`: Creates activity and audit trail logs

## Models Covered

### User Management

- **UserRole**: Admin, Engineer, Viewer roles
- **Permission**: System permissions (project_read, project_write, etc.)
- **RolePermission**: Permission assignments to roles
- **User**: Sample user accounts (admin, engineer, viewer)
- **UserPreference**: User-specific preferences
- **UserRoleAssignment**: Role assignments to users

### Project Management

- **Project**: Sample electrical projects
- **ProjectMember**: User assignments to projects
- **ProjectPhase**: Standard electrical project phases
- **ProjectMilestone**: Phase-specific milestones
- **ProjectTemplate**: Reusable project templates

### Component Management

- **ComponentCategory**: Electrical component categories
- **ComponentType**: Specific component types
- **Component**: Sample components for each type

### Task Management

- **Task**: Sample project tasks
- **TaskAssignment**: Task assignments to users

### System Configuration

- **SystemConfiguration**: Global and project-specific configurations

### Synchronization

- **SynchronizationLog**: Sample sync operations
- **SynchronizationConflict**: Sample sync conflicts

### Activity Tracking

- **ActivityLog**: User activity logs
- **AuditTrail**: System audit trails

## Sample Data Created

### Users

- **admin**: Administrator with full permissions
- **engineer**: Engineer with project and component permissions
- **viewer**: Read-only user with limited permissions

### Projects

- **Industrial Plant Expansion**: Manufacturing facility project
- **Commercial Building Upgrade**: Office building electrical upgrade

### Project Phases

- Planning
- Conceptual Design
- Schematic Design
- Design Development

### Components

- One sample component for each ComponentType enum value
- Realistic electrical component specifications

### Tasks

- Load analysis tasks
- Design tasks
- Review tasks

## Usage

### Running the Seeding Script

```bash
cd server/src
uv run python main.py seed-general-data
```

### Prerequisites

- Database must be initialized and migrated
- All required dependencies must be installed
- Environment variables must be configured

### Idempotent Operation

The script can be run multiple times safely. It checks for existing data and only creates new records when necessary.

## Dependencies and Order

The seeding follows a strict dependency order:

1. **Roles** → No dependencies
2. **Permissions** → No dependencies
3. **Users** → Depends on roles
4. **Role Permissions** → Depends on roles and permissions
5. **Component Infrastructure** → No dependencies
6. **Projects** → No dependencies
7. **Project Members** → Depends on users and projects
8. **Project Phases** → Depends on projects
9. **Project Milestones** → Depends on project phases
10. **Tasks** → Depends on projects and users
11. **System Configurations** → Depends on projects (optional)
12. **Synchronization Logs** → Depends on projects and users
13. **Activity Logs** → Depends on users

## Configuration

### Enum Values Used

- **FrequencyType**: HZ_60 for North American systems
- **TemperatureUnit**: CELSIUS for metric measurements
- **TaskPriority**: HIGH, MEDIUM, LOW
- **TaskStatus**: COMPLETED, IN_PROGRESS, NOT_STARTED
- **ProjectPhaseType**: PLANNING, CONCEPTUAL_DESIGN, etc.

### Sample Data Characteristics

- Realistic electrical engineering data
- Professional naming conventions
- Industry-standard specifications
- Proper relationships and constraints

## Error Handling

The script includes comprehensive error handling:

- Database constraint validation
- Enum type validation
- Relationship integrity checks
- Detailed logging for troubleshooting

## Future Enhancements

### Planned Additions

- ElectricalStandard model seeding
- UserPreferences model seeding (extended)
- ConfigurationTemplate model seeding

These models are temporarily commented out and will be added in future migrations once the basic SystemConfiguration
functionality is stable.

## Maintenance

### Adding New Seed Methods

1. Create the seed method following the naming pattern `seed_<model_name>()`
2. Add proper dependency checking
3. Include the method in the main `seed_general_data()` function
4. Update this documentation

### Modifying Existing Data

- Update the seed methods to reflect model changes
- Ensure backward compatibility
- Test thoroughly in development environment

## Troubleshooting

### Common Issues

- **Enum validation errors**: Ensure enum values match model definitions
- **Foreign key constraints**: Check dependency order
- **Duplicate data**: Verify idempotent checks are working

### Debugging

- Check application logs for detailed error messages
- Verify database schema is up to date
- Ensure all migrations have been applied

## Performance

The seeding script is optimized for:

- Batch operations where possible
- Minimal database queries
- Efficient relationship handling
- Fast execution for development workflows

Typical execution time: 2-5 seconds for full seeding operation.
